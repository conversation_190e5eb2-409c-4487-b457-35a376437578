# Go 并发编程面试题笔记

## 目录
1. [Goroutine 基础](#1-goroutine-基础)
2. [Channel 通信](#2-channel-通信)
3. [同步原语](#3-同步原语)
4. [并发控制](#4-并发控制)
5. [并发模式](#5-并发模式)
6. [性能优化](#6-性能优化)
7. [常见陷阱](#7-常见陷阱)
8. [实战应用](#8-实战应用)

---

## 1. Goroutine 基础

### 1.1 什么是 Goroutine？

**问题：** Goroutine 的特点和优势是什么？

**答案：** Goroutine 是 Go 语言的轻量级线程，由 Go 运行时管理

**核心特点：**
- **轻量级**：初始栈大小只有 2KB，可动态增长
- **高并发**：单机可创建数百万个 goroutine
- **调度器**：M:N 调度模型，M 个 goroutine 映射到 N 个 OS 线程
- **抢占式**：Go 1.14+ 支持异步抢占

```go
// 创建 goroutine
go func() {
    fmt.Println("Hello from goroutine!")
}()

// 带参数的 goroutine
go func(name string) {
    fmt.Printf("Hello %s\n", name)
}("World")

// 等待 goroutine 完成
var wg sync.WaitGroup
wg.Add(1)
go func() {
    defer wg.Done()
    fmt.Println("Work done!")
}()
wg.Wait()
```

### 1.2 Goroutine 调度器（GMP 模型）

**问题：** Go 的调度器是如何工作的？

**答案：** Go 使用 GMP 调度模型

**组件说明：**
- **G (Goroutine)**：用户级线程，包含栈、程序计数器等
- **M (Machine)**：OS 线程，真正执行计算的实体
- **P (Processor)**：逻辑处理器，连接 G 和 M 的桥梁

```go
// 查看和设置 GOMAXPROCS
import "runtime"

func main() {
    // 获取当前 GOMAXPROCS 值
    fmt.Printf("GOMAXPROCS: %d\n", runtime.GOMAXPROCS(0))

    // 设置 GOMAXPROCS（通常不需要手动设置）
    runtime.GOMAXPROCS(4)

    // 获取当前 goroutine 数量
    fmt.Printf("Goroutines: %d\n", runtime.NumGoroutine())
}
```

**调度时机：**
1. 主动让出：`runtime.Gosched()`
2. 系统调用：文件 I/O、网络 I/O
3. 阻塞操作：channel 操作、锁等待
4. 抢占调度：长时间运行的 goroutine

### 1.3 Goroutine 生命周期

```go
func goroutineLifecycle() {
    fmt.Println("主 goroutine 开始")

    // 创建子 goroutine
    go func() {
        fmt.Println("子 goroutine 开始")
        time.Sleep(1 * time.Second)
        fmt.Println("子 goroutine 结束")
    }()

    // 主 goroutine 继续执行
    fmt.Println("主 goroutine 继续")
    time.Sleep(2 * time.Second)
    fmt.Println("主 goroutine 结束")
}
```

---

## 2. Channel 通信

### 2.1 Channel 基础

**问题：** Channel 的作用和类型有哪些？

**答案：** Channel 是 goroutine 之间通信的管道

**Channel 类型：**
- **无缓冲 Channel**：同步通信，发送和接收必须同时准备好
- **有缓冲 Channel**：异步通信，缓冲区未满时发送不阻塞

```go
// 无缓冲 channel（同步）
ch1 := make(chan int)

// 有缓冲 channel（异步）
ch2 := make(chan int, 10)

// 只读 channel
var readOnly <-chan int = ch1

// 只写 channel
var writeOnly chan<- int = ch1

// 双向 channel
var bidirectional chan int = ch1
```

### 2.2 Channel 操作

```go
func channelOperations() {
    ch := make(chan int, 3)

    // 发送数据
    ch <- 1
    ch <- 2
    ch <- 3

    // 接收数据
    value1 := <-ch
    value2, ok := <-ch  // ok 表示 channel 是否已关闭

    fmt.Printf("value1: %d, value2: %d, ok: %v\n", value1, value2, ok)

    // 关闭 channel
    close(ch)

    // 从已关闭的 channel 接收数据
    value3, ok := <-ch
    fmt.Printf("value3: %d, ok: %v\n", value3, ok)  // value3: 3, ok: true

    value4, ok := <-ch
    fmt.Printf("value4: %d, ok: %v\n", value4, ok)  // value4: 0, ok: false
}
```

### 2.3 Select 语句

**问题：** Select 语句的作用和使用场景？

**答案：** Select 用于处理多个 channel 操作，类似于 switch 但用于 channel

```go
func selectExample() {
    ch1 := make(chan string)
    ch2 := make(chan string)

    go func() {
        time.Sleep(1 * time.Second)
        ch1 <- "from ch1"
    }()

    go func() {
        time.Sleep(2 * time.Second)
        ch2 <- "from ch2"
    }()

    for i := 0; i < 2; i++ {
        select {
        case msg1 := <-ch1:
            fmt.Println("Received:", msg1)
        case msg2 := <-ch2:
            fmt.Println("Received:", msg2)
        case <-time.After(3 * time.Second):
            fmt.Println("Timeout!")
            return
        default:
            fmt.Println("No channel ready")
            time.Sleep(500 * time.Millisecond)
        }
    }
}
```

---

## 3. 同步原语

### 3.1 sync.WaitGroup

**问题：** WaitGroup 的作用和使用方法？

**答案：** WaitGroup 用于等待一组 goroutine 完成

```go
func waitGroupExample() {
    var wg sync.WaitGroup

    // 启动多个 goroutine
    for i := 0; i < 5; i++ {
        wg.Add(1)  // 增加计数
        go func(id int) {
            defer wg.Done()  // 减少计数
            fmt.Printf("Worker %d starting\n", id)
            time.Sleep(time.Duration(id) * time.Second)
            fmt.Printf("Worker %d done\n", id)
        }(i)
    }

    wg.Wait()  // 等待所有 goroutine 完成
    fmt.Println("All workers done")
}
```

**注意事项：**
- `Add()` 必须在 `Wait()` 之前调用
- `Done()` 通常使用 `defer` 调用
- 不要在 goroutine 内部调用 `Add()`

### 3.2 sync.Mutex

**问题：** Mutex 的作用和使用场景？

**答案：** Mutex（互斥锁）用于保护共享资源，确保同时只有一个 goroutine 访问

```go
type Counter struct {
    mu    sync.Mutex
    value int
}

func (c *Counter) Increment() {
    c.mu.Lock()
    defer c.mu.Unlock()
    c.value++
}

func (c *Counter) Value() int {
    c.mu.Lock()
    defer c.mu.Unlock()
    return c.value
}

func mutexExample() {
    counter := &Counter{}
    var wg sync.WaitGroup

    // 启动多个 goroutine 并发增加计数
    for i := 0; i < 1000; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            counter.Increment()
        }()
    }

    wg.Wait()
    fmt.Printf("Final counter value: %d\n", counter.Value())
}
```

### 3.3 sync.RWMutex

**问题：** RWMutex 与 Mutex 的区别？

**答案：** RWMutex（读写锁）允许多个读操作并发，但写操作独占

```go
type SafeMap struct {
    mu   sync.RWMutex
    data map[string]int
}

func NewSafeMap() *SafeMap {
    return &SafeMap{
        data: make(map[string]int),
    }
}

func (sm *SafeMap) Get(key string) (int, bool) {
    sm.mu.RLock()  // 读锁
    defer sm.mu.RUnlock()
    value, ok := sm.data[key]
    return value, ok
}

func (sm *SafeMap) Set(key string, value int) {
    sm.mu.Lock()  // 写锁
    defer sm.mu.Unlock()
    sm.data[key] = value
}
```

### 3.4 sync.Once

**问题：** sync.Once 的作用和使用场景？

**答案：** sync.Once 确保某个操作只执行一次，常用于单例模式

```go
type Singleton struct {
    data string
}

var (
    instance *Singleton
    once     sync.Once
)

func GetInstance() *Singleton {
    once.Do(func() {
        fmt.Println("Creating singleton instance")
        instance = &Singleton{data: "singleton"}
    })
    return instance
}

func onceExample() {
    var wg sync.WaitGroup

    // 多个 goroutine 同时获取单例
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            singleton := GetInstance()
            fmt.Printf("Goroutine %d got: %p\n", id, singleton)
        }(i)
    }

    wg.Wait()
}
```

### 3.5 sync.Cond

**问题：** sync.Cond 的作用和使用场景？

**答案：** sync.Cond 用于协调多个 goroutine 的等待和唤醒

```go
func condExample() {
    var mu sync.Mutex
    cond := sync.NewCond(&mu)
    ready := false

    // 启动等待的 goroutine
    for i := 0; i < 3; i++ {
        go func(id int) {
            mu.Lock()
            defer mu.Unlock()

            for !ready {
                fmt.Printf("Worker %d waiting...\n", id)
                cond.Wait()  // 等待条件满足
            }

            fmt.Printf("Worker %d working...\n", id)
        }(i)
    }

    time.Sleep(2 * time.Second)

    // 设置条件并唤醒所有等待者
    mu.Lock()
    ready = true
    mu.Unlock()
    cond.Broadcast()  // 唤醒所有等待的 goroutine

    time.Sleep(1 * time.Second)
}
```

---

## 4. 并发控制

### 4.1 控制并发数量

**问题：** 如何控制并发的 goroutine 数量？

**答案：** 使用有缓冲的 channel 作为信号量

```go
func concurrencyControl() {
    const maxConcurrency = 3
    const totalTasks = 10

    // 创建信号量
    semaphore := make(chan struct{}, maxConcurrency)
    var wg sync.WaitGroup

    for i := 0; i < totalTasks; i++ {
        wg.Add(1)
        go func(taskID int) {
            defer wg.Done()

            // 获取信号量
            semaphore <- struct{}{}
            defer func() { <-semaphore }()  // 释放信号量

            // 执行任务
            fmt.Printf("Task %d starting\n", taskID)
            time.Sleep(2 * time.Second)
            fmt.Printf("Task %d completed\n", taskID)
        }(i)
    }

    wg.Wait()
    fmt.Println("All tasks completed")
}
```

### 4.2 超时控制

```go
func timeoutControl() {
    ch := make(chan string, 1)

    go func() {
        time.Sleep(3 * time.Second)
        ch <- "result"
    }()

    select {
    case result := <-ch:
        fmt.Printf("Got result: %s\n", result)
    case <-time.After(2 * time.Second):
        fmt.Println("Operation timed out")
    }
}
```

### 4.3 Context 控制

```go
func contextControl() {
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()

    var wg sync.WaitGroup

    for i := 0; i < 3; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            worker(ctx, id)
        }(i)
    }

    wg.Wait()
}

func worker(ctx context.Context, id int) {
    for {
        select {
        case <-ctx.Done():
            fmt.Printf("Worker %d stopped: %v\n", id, ctx.Err())
            return
        default:
            fmt.Printf("Worker %d working...\n", id)
            time.Sleep(1 * time.Second)
        }
    }
}
```

---

## 5. 并发模式

### 5.1 生产者-消费者模式

```go
func producerConsumer() {
    ch := make(chan int, 10)
    var wg sync.WaitGroup

    // 生产者
    wg.Add(1)
    go func() {
        defer wg.Done()
        defer close(ch)

        for i := 0; i < 20; i++ {
            ch <- i
            fmt.Printf("Produced: %d\n", i)
            time.Sleep(100 * time.Millisecond)
        }
    }()

    // 消费者
    for i := 0; i < 3; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()

            for value := range ch {
                fmt.Printf("Consumer %d consumed: %d\n", id, value)
                time.Sleep(200 * time.Millisecond)
            }
        }(i)
    }

    wg.Wait()
}
```

### 5.2 工作池模式

```go
type Job struct {
    ID   int
    Data string
}

type Result struct {
    Job Job
    Sum int
}

func workerPool() {
    const numWorkers = 3
    const numJobs = 10

    jobs := make(chan Job, numJobs)
    results := make(chan Result, numJobs)

    // 启动工作者
    for w := 1; w <= numWorkers; w++ {
        go worker(w, jobs, results)
    }

    // 发送任务
    for j := 1; j <= numJobs; j++ {
        jobs <- Job{ID: j, Data: fmt.Sprintf("task-%d", j)}
    }
    close(jobs)

    // 收集结果
    for r := 1; r <= numJobs; r++ {
        result := <-results
        fmt.Printf("Job %d result: %d\n", result.Job.ID, result.Sum)
    }
}

func worker(id int, jobs <-chan Job, results chan<- Result) {
    for job := range jobs {
        fmt.Printf("Worker %d processing job %d\n", id, job.ID)

        // 模拟工作
        sum := 0
        for i := 0; i < job.ID*100; i++ {
            sum += i
        }

        results <- Result{Job: job, Sum: sum}
    }
}
```

### 5.3 扇入扇出模式

```go
// 扇出：一个输入分发到多个输出
func fanOut(input <-chan int, workers int) []<-chan int {
    outputs := make([]<-chan int, workers)

    for i := 0; i < workers; i++ {
        output := make(chan int)
        outputs[i] = output

        go func(out chan<- int) {
            defer close(out)
            for value := range input {
                out <- value * value  // 计算平方
            }
        }(output)
    }

    return outputs
}

// 扇入：多个输入合并到一个输出
func fanIn(inputs ...<-chan int) <-chan int {
    output := make(chan int)
    var wg sync.WaitGroup

    for _, input := range inputs {
        wg.Add(1)
        go func(in <-chan int) {
            defer wg.Done()
            for value := range in {
                output <- value
            }
        }(input)
    }

    go func() {
        wg.Wait()
        close(output)
    }()

    return output
}
```

---

## 6. 性能优化

### 6.1 Goroutine 池

**问题：** 如何避免创建过多的 goroutine？

**答案：** 使用 goroutine 池复用 goroutine

```go
type GoroutinePool struct {
    workers    int
    taskQueue  chan func()
    wg         sync.WaitGroup
}

func NewGoroutinePool(workers int) *GoroutinePool {
    pool := &GoroutinePool{
        workers:   workers,
        taskQueue: make(chan func(), workers*2),
    }

    pool.start()
    return pool
}

func (p *GoroutinePool) start() {
    for i := 0; i < p.workers; i++ {
        p.wg.Add(1)
        go func() {
            defer p.wg.Done()
            for task := range p.taskQueue {
                task()
            }
        }()
    }
}

func (p *GoroutinePool) Submit(task func()) {
    p.taskQueue <- task
}

func (p *GoroutinePool) Close() {
    close(p.taskQueue)
    p.wg.Wait()
}

func poolExample() {
    pool := NewGoroutinePool(3)
    defer pool.Close()

    for i := 0; i < 10; i++ {
        taskID := i
        pool.Submit(func() {
            fmt.Printf("Task %d executed\n", taskID)
            time.Sleep(1 * time.Second)
        })
    }
}
```

### 6.2 内存优化

```go
// 避免 goroutine 泄漏
func avoidGoroutineLeak() {
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()  // 确保取消函数被调用

    ch := make(chan int)

    go func() {
        defer close(ch)
        for i := 0; i < 100; i++ {
            select {
            case ch <- i:
            case <-ctx.Done():
                return  // 避免 goroutine 泄漏
            }
        }
    }()

    // 消费数据
    for {
        select {
        case value, ok := <-ch:
            if !ok {
                return
            }
            fmt.Printf("Received: %d\n", value)
        case <-ctx.Done():
            return
        }
    }
}
```

### 6.3 减少锁竞争

```go
// 使用分片减少锁竞争
type ShardedMap struct {
    shards []*MapShard
    count  int
}

type MapShard struct {
    mu   sync.RWMutex
    data map[string]interface{}
}

func NewShardedMap(shardCount int) *ShardedMap {
    shards := make([]*MapShard, shardCount)
    for i := 0; i < shardCount; i++ {
        shards[i] = &MapShard{
            data: make(map[string]interface{}),
        }
    }

    return &ShardedMap{
        shards: shards,
        count:  shardCount,
    }
}

func (sm *ShardedMap) getShard(key string) *MapShard {
    hash := fnv.New32a()
    hash.Write([]byte(key))
    return sm.shards[hash.Sum32()%uint32(sm.count)]
}

func (sm *ShardedMap) Set(key string, value interface{}) {
    shard := sm.getShard(key)
    shard.mu.Lock()
    defer shard.mu.Unlock()
    shard.data[key] = value
}

func (sm *ShardedMap) Get(key string) (interface{}, bool) {
    shard := sm.getShard(key)
    shard.mu.RLock()
    defer shard.mu.RUnlock()
    value, ok := shard.data[key]
    return value, ok
}
```

---

## 7. 常见陷阱

### 7.1 Goroutine 闭包陷阱

**问题：** for 循环中启动 goroutine 的常见错误？

**答案：** 闭包捕获了循环变量的引用，而不是值

```go
// ❌ 错误示例
func closureTrapWrong() {
    for i := 0; i < 5; i++ {
        go func() {
            fmt.Printf("Wrong: %d\n", i)  // 可能都打印 5
        }()
    }
    time.Sleep(1 * time.Second)
}

// ✅ 正确示例1：参数传递
func closureTrapCorrect1() {
    for i := 0; i < 5; i++ {
        go func(val int) {
            fmt.Printf("Correct1: %d\n", val)
        }(i)
    }
    time.Sleep(1 * time.Second)
}

// ✅ 正确示例2：局部变量
func closureTrapCorrect2() {
    for i := 0; i < 5; i++ {
        val := i
        go func() {
            fmt.Printf("Correct2: %d\n", val)
        }()
    }
    time.Sleep(1 * time.Second)
}
```

### 7.2 Channel 死锁

```go
// ❌ 死锁示例
func deadlockExample() {
    ch := make(chan int)
    ch <- 1  // 死锁：无缓冲 channel，没有接收者
    fmt.Println(<-ch)
}

// ✅ 正确示例
func noDeadlock() {
    ch := make(chan int, 1)  // 有缓冲
    ch <- 1
    fmt.Println(<-ch)
}
```

### 7.3 资源泄漏

```go
// ❌ 资源泄漏
func resourceLeakWrong() {
    ch := make(chan int)

    go func() {
        for i := 0; i < 1000000; i++ {
            ch <- i  // 如果没有接收者，goroutine 会一直阻塞
        }
    }()

    // 只接收一个值就退出
    fmt.Println(<-ch)
}

// ✅ 正确示例
func resourceLeakCorrect() {
    ch := make(chan int)
    done := make(chan bool)

    go func() {
        defer close(ch)
        for i := 0; i < 1000000; i++ {
            select {
            case ch <- i:
            case <-done:
                return  // 收到退出信号
            }
        }
    }()

    // 接收一个值
    fmt.Println(<-ch)

    // 发送退出信号
    close(done)
}
```

### 7.4 竞态条件

```go
// ❌ 竞态条件
var counter int

func raceConditionWrong() {
    var wg sync.WaitGroup

    for i := 0; i < 1000; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            counter++  // 竞态条件
        }()
    }

    wg.Wait()
    fmt.Printf("Counter: %d\n", counter)  // 结果不确定
}

// ✅ 正确示例
func raceConditionCorrect() {
    var counter int64
    var wg sync.WaitGroup

    for i := 0; i < 1000; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            atomic.AddInt64(&counter, 1)  // 原子操作
        }()
    }

    wg.Wait()
    fmt.Printf("Counter: %d\n", atomic.LoadInt64(&counter))
}
```

---

## 8. 实战应用

### 8.1 HTTP 服务器并发处理

```go
func httpServerExample() {
    // 限制并发连接数
    semaphore := make(chan struct{}, 100)

    http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
        // 获取信号量
        semaphore <- struct{}{}
        defer func() { <-semaphore }()

        // 处理请求
        processRequest(w, r)
    })

    log.Fatal(http.ListenAndServe(":8080", nil))
}

func processRequest(w http.ResponseWriter, r *http.Request) {
    // 模拟处理时间
    time.Sleep(100 * time.Millisecond)
    w.Write([]byte("Hello, World!"))
}
```

### 8.2 批量数据处理

```go
func batchProcessing() {
    data := generateData(10000)

    const batchSize = 100
    const workers = 10

    jobs := make(chan []int, workers)
    results := make(chan int, workers)

    // 启动工作者
    var wg sync.WaitGroup
    for i := 0; i < workers; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for batch := range jobs {
                result := processBatch(batch)
                results <- result
            }
        }()
    }

    // 发送批次任务
    go func() {
        defer close(jobs)
        for i := 0; i < len(data); i += batchSize {
            end := i + batchSize
            if end > len(data) {
                end = len(data)
            }
            jobs <- data[i:end]
        }
    }()

    // 收集结果
    go func() {
        wg.Wait()
        close(results)
    }()

    totalResult := 0
    for result := range results {
        totalResult += result
    }

    fmt.Printf("Total result: %d\n", totalResult)
}

func generateData(size int) []int {
    data := make([]int, size)
    for i := 0; i < size; i++ {
        data[i] = i + 1
    }
    return data
}

func processBatch(batch []int) int {
    sum := 0
    for _, v := range batch {
        sum += v
    }
    return sum
}
```

### 8.3 缓存系统

```go
type Cache struct {
    mu    sync.RWMutex
    data  map[string]*CacheItem
    ttl   time.Duration
}

type CacheItem struct {
    value     interface{}
    expiredAt time.Time
}

func NewCache(ttl time.Duration) *Cache {
    cache := &Cache{
        data: make(map[string]*CacheItem),
        ttl:  ttl,
    }

    // 启动清理 goroutine
    go cache.cleanup()

    return cache
}

func (c *Cache) Set(key string, value interface{}) {
    c.mu.Lock()
    defer c.mu.Unlock()

    c.data[key] = &CacheItem{
        value:     value,
        expiredAt: time.Now().Add(c.ttl),
    }
}

func (c *Cache) Get(key string) (interface{}, bool) {
    c.mu.RLock()
    defer c.mu.RUnlock()

    item, exists := c.data[key]
    if !exists {
        return nil, false
    }

    if time.Now().After(item.expiredAt) {
        return nil, false
    }

    return item.value, true
}

func (c *Cache) cleanup() {
    ticker := time.NewTicker(time.Minute)
    defer ticker.Stop()

    for range ticker.C {
        c.mu.Lock()
        now := time.Now()
        for key, item := range c.data {
            if now.After(item.expiredAt) {
                delete(c.data, key)
            }
        }
        c.mu.Unlock()
    }
}
```

---

## 9. 面试常考题目

### 9.1 如何优雅地关闭 goroutine？

```go
func gracefulShutdown() {
    ctx, cancel := context.WithCancel(context.Background())
    var wg sync.WaitGroup

    // 启动工作者
    for i := 0; i < 3; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            worker(ctx, id)
        }(i)
    }

    // 模拟运行一段时间
    time.Sleep(5 * time.Second)

    // 发送关闭信号
    cancel()

    // 等待所有工作者退出
    wg.Wait()
    fmt.Println("All workers stopped gracefully")
}
```

### 9.2 如何检测 goroutine 泄漏？

```go
func detectGoroutineLeak() {
    fmt.Printf("Initial goroutines: %d\n", runtime.NumGoroutine())

    // 创建一些 goroutine
    for i := 0; i < 10; i++ {
        go func() {
            time.Sleep(1 * time.Second)
        }()
    }

    time.Sleep(2 * time.Second)
    fmt.Printf("Final goroutines: %d\n", runtime.NumGoroutine())
}
```

### 9.3 多个 goroutine 对同一个 map 写会 panic，如何解决？

```go
// ❌ 会 panic
func mapPanic() {
    m := make(map[int]int)
    var wg sync.WaitGroup

    for i := 0; i < 100; i++ {
        wg.Add(1)
        go func(val int) {
            defer wg.Done()
            m[val] = val  // 并发写 map 会 panic
        }(i)
    }

    wg.Wait()
}

// ✅ 解决方案1：使用 sync.Map
func useSyncMap() {
    var m sync.Map
    var wg sync.WaitGroup

    for i := 0; i < 100; i++ {
        wg.Add(1)
        go func(val int) {
            defer wg.Done()
            m.Store(val, val)
        }(i)
    }

    wg.Wait()
}

// ✅ 解决方案2：使用互斥锁
func useMapWithMutex() {
    m := make(map[int]int)
    var mu sync.Mutex
    var wg sync.WaitGroup

    for i := 0; i < 100; i++ {
        wg.Add(1)
        go func(val int) {
            defer wg.Done()
            mu.Lock()
            m[val] = val
            mu.Unlock()
        }(i)
    }

    wg.Wait()
}
```

---

## 10. 总结

### 10.1 并发编程最佳实践

1. **优先使用 channel 进行通信**，而不是共享内存
2. **合理控制 goroutine 数量**，避免创建过多 goroutine
3. **及时释放资源**，避免 goroutine 泄漏
4. **使用 context 进行超时和取消控制**
5. **避免在循环中直接使用循环变量**
6. **使用 sync 包提供的同步原语保护共享资源**

### 10.2 性能考虑

1. **Goroutine 创建成本低，但不是零成本**
2. **Channel 操作有一定开销，适合低频通信**
3. **Mutex 性能高于 channel，适合保护临界区**
4. **原子操作性能最高，适合简单的计数器**

### 10.3 调试技巧

1. **使用 `go run -race` 检测竞态条件**
2. **使用 `runtime.NumGoroutine()` 监控 goroutine 数量**
3. **使用 pprof 分析性能瓶颈**
4. **使用 context 设置超时避免死锁**

这份笔记涵盖了 Go 并发编程的核心概念、常见模式、性能优化和实战应用，是面试和实际开发的重要参考资料。