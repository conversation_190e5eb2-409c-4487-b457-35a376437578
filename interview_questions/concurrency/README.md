# Go 并发编程代码示例

这个目录包含了 Go 并发编程的完整代码示例，涵盖了从基础到高级的各种并发模式和技术。

## 📁 文件说明

- `concurrency_test.go` - 完整的并发代码示例和测试
- `concurrency.md` - 详细的并发编程笔记
- `深入浅出进程线程协程.md` - 通俗易懂的概念解释
- `面试回答-进程线程协程区别.md` - 面试回答模板
- `go.mod` - Go 模块文件

## 🚀 运行方式

### 运行所有测试
```bash
cd interview_questions/concurrency
go test -v
```

### 运行特定测试
```bash
# 运行基础 Goroutine 测试
go test -v -run TestBasicGoroutine

# 运行 Channel 相关测试
go test -v -run TestBasicChannel

# 运行并发模式测试
go test -v -run TestProducerConsumer
```

### 运行性能基准测试
```bash
# 运行所有基准测试
go test -bench=.

# 比较 Channel vs Mutex 性能
go test -bench=BenchmarkChannelVsMutex

# 测试 Goroutine 创建性能
go test -bench=BenchmarkGoroutineCreation
```

### 检测竞态条件
```bash
go test -race -v
```

## 📚 代码示例分类

### 1. 基础概念 (Basic Concepts)
- `TestBasicGoroutine` - Goroutine 基础使用
- `TestGoroutineClosureTrap` - 闭包陷阱演示
- `TestBasicChannel` - Channel 基础操作
- `TestChannelDirections` - Channel 方向控制
- `TestSelectStatement` - Select 语句使用

### 2. 同步原语 (Synchronization Primitives)
- `TestMutex` - 互斥锁使用
- `TestRWMutex` - 读写锁使用
- `TestOnce` - sync.Once 单例模式
- `TestWaitGroup` - 等待组使用
- `TestAtomicOperations` - 原子操作

### 3. 并发模式 (Concurrency Patterns)
- `TestProducerConsumer` - 生产者消费者模式
- `TestWorkerPool` - 工作池模式
- `TestFanInFanOut` - 扇入扇出模式
- `TestPipeline` - 管道模式

### 4. 并发控制 (Concurrency Control)
- `TestConcurrencyControl` - 并发数量控制
- `TestContextControl` - Context 控制
- `TestRateLimiter` - 限流器
- `TestCircuitBreaker` - 断路器模式

### 5. 实际应用 (Real-world Applications)
- `TestWebServerSimulation` - Web 服务器模拟
- `TestCrawlerSimulation` - 爬虫并发抓取
- `TestChatRoomSimulation` - 聊天室模拟

### 6. 常见陷阱 (Common Pitfalls)
- `TestRaceCondition` - 竞态条件演示
- `TestDeadlock` - 死锁预防
- `TestGoroutineLeak` - Goroutine 泄漏防范

### 7. 性能测试 (Performance Testing)
- `BenchmarkChannelVsMutex` - 性能对比
- `BenchmarkGoroutineCreation` - 创建性能
- `TestGoroutineMemoryUsage` - 内存使用测试

## 🎯 学习建议

### 初学者路径：
1. 先运行基础测试，理解 Goroutine 和 Channel
2. 学习同步原语的使用场景
3. 掌握常见的并发模式
4. 了解常见陷阱和如何避免

### 进阶路径：
1. 深入理解 GMP 调度模型
2. 学习高级并发模式
3. 进行性能测试和优化
4. 在实际项目中应用

### 面试准备：
1. 熟练掌握基础概念
2. 能够解释各种并发模式的适用场景
3. 了解常见问题的解决方案
4. 准备实际项目经验分享

## 🔧 调试技巧

### 1. 检测竞态条件
```bash
go test -race -v
```

### 2. 查看 Goroutine 数量
```go
fmt.Printf("当前 Goroutine 数量: %d\n", runtime.NumGoroutine())
```

### 3. 内存使用分析
```bash
go test -memprofile=mem.prof
go tool pprof mem.prof
```

### 4. CPU 性能分析
```bash
go test -cpuprofile=cpu.prof
go tool pprof cpu.prof
```

## 📖 扩展阅读

- [Go 官方并发教程](https://tour.golang.org/concurrency/1)
- [Effective Go - 并发](https://golang.org/doc/effective_go.html#concurrency)
- [Go 并发模式](https://blog.golang.org/pipelines)
- [高级 Go 并发模式](https://blog.golang.org/advanced-go-concurrency-patterns)

## 💡 最佳实践

1. **优先使用 Channel 进行通信**，而不是共享内存
2. **合理控制 Goroutine 数量**，避免创建过多
3. **及时释放资源**，防止 Goroutine 泄漏
4. **使用 Context 进行超时和取消控制**
5. **在循环中避免直接使用循环变量**
6. **使用 sync 包的同步原语保护共享资源**

## 🚨 注意事项

- 运行测试时可能会看到一些"错误"输出，这些是故意演示错误用法的示例
- 某些测试（如死锁示例）已经注释掉危险代码，避免真正的死锁
- 性能测试结果可能因机器配置而异
- 建议在理解概念后再运行代码，效果更佳

Happy Coding! 🎉
