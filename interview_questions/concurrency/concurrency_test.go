package concurrency_test

import (
	"context"
	"fmt"
	"math/rand"
	"net/http"
	"runtime"
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

// ============================================================================
// 1. Goroutine 基础示例
// ============================================================================

// TestBasicGoroutine 演示基本的 goroutine 创建和使用
func TestBasicGoroutine(t *testing.T) {
	fmt.Println("\n=== 基本 Goroutine 示例 ===")

	// 记录初始 goroutine 数量
	initialGoroutines := runtime.NumGoroutine()
	fmt.Printf("初始 goroutine 数量: %d\n", initialGoroutines)

	var wg sync.WaitGroup

	// 创建多个 goroutine
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			fmt.Printf("Goroutine %d 开始工作\n", id)
			time.Sleep(time.Duration(rand.Intn(1000)) * time.Millisecond)
			fmt.Printf("Goroutine %d 完成工作\n", id)
		}(i)
	}

	// 检查运行中的 goroutine 数量
	runningGoroutines := runtime.NumGoroutine()
	fmt.Printf("运行中 goroutine 数量: %d\n", runningGoroutines)

	wg.Wait()

	// 等待一下让 goroutine 完全结束
	time.Sleep(100 * time.Millisecond)
	finalGoroutines := runtime.NumGoroutine()
	fmt.Printf("最终 goroutine 数量: %d\n", finalGoroutines)
}

// TestGoroutineClosureTrap 演示 goroutine 闭包陷阱
func TestGoroutineClosureTrap(t *testing.T) {
	fmt.Println("\n=== Goroutine 闭包陷阱示例 ===")

	// ❌ 错误示例：闭包陷阱
	fmt.Println("错误示例（闭包陷阱）:")
	var wg sync.WaitGroup
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			fmt.Printf("错误: i = %d\n", i) // 可能都打印 5
		}()
	}
	wg.Wait()

	time.Sleep(100 * time.Millisecond)

	// ✅ 正确示例1：参数传递
	fmt.Println("\n正确示例1（参数传递）:")
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(val int) {
			defer wg.Done()
			fmt.Printf("正确1: val = %d\n", val)
		}(i)
	}
	wg.Wait()

	time.Sleep(100 * time.Millisecond)

	// ✅ 正确示例2：局部变量
	fmt.Println("\n正确示例2（局部变量）:")
	for i := 0; i < 5; i++ {
		wg.Add(1)
		val := i // 创建局部变量
		go func() {
			defer wg.Done()
			fmt.Printf("正确2: val = %d\n", val)
		}()
	}
	wg.Wait()
}

// ============================================================================
// 2. Channel 基础示例
// ============================================================================

// TestBasicChannel 演示基本的 channel 使用
func TestBasicChannel(t *testing.T) {
	fmt.Println("\n=== 基本 Channel 示例 ===")

	// 无缓冲 channel
	fmt.Println("无缓冲 Channel:")
	ch1 := make(chan string)

	go func() {
		time.Sleep(1 * time.Second)
		ch1 <- "Hello from unbuffered channel"
	}()

	message := <-ch1
	fmt.Printf("接收到: %s\n", message)

	// 有缓冲 channel
	fmt.Println("\n有缓冲 Channel:")
	ch2 := make(chan int, 3)

	// 发送数据（不会阻塞）
	ch2 <- 1
	ch2 <- 2
	ch2 <- 3

	// 接收数据
	for i := 0; i < 3; i++ {
		value := <-ch2
		fmt.Printf("接收到: %d\n", value)
	}

	// 关闭 channel
	fmt.Println("\n关闭 Channel:")
	ch3 := make(chan int, 2)
	ch3 <- 10
	ch3 <- 20
	close(ch3)

	// 从已关闭的 channel 接收数据
	for {
		value, ok := <-ch3
		if !ok {
			fmt.Println("Channel 已关闭")
			break
		}
		fmt.Printf("从已关闭 channel 接收: %d\n", value)
	}
}

// TestChannelDirections 演示 channel 方向
func TestChannelDirections(t *testing.T) {
	fmt.Println("\n=== Channel 方向示例 ===")

	ch := make(chan string, 1)

	// 只写 channel
	go func(sendCh chan<- string) {
		sendCh <- "Hello"
		fmt.Println("数据已发送")
	}(ch)

	// 只读 channel
	go func(recvCh <-chan string) {
		message := <-recvCh
		fmt.Printf("接收到: %s\n", message)
	}(ch)

	time.Sleep(100 * time.Millisecond)
}

// ============================================================================
// 3. Select 语句示例
// ============================================================================

// TestSelectStatement 演示 select 语句的使用
func TestSelectStatement(t *testing.T) {
	fmt.Println("\n=== Select 语句示例 ===")

	ch1 := make(chan string)
	ch2 := make(chan string)

	// 启动两个 goroutine 发送数据
	go func() {
		time.Sleep(1 * time.Second)
		ch1 <- "来自 channel 1"
	}()

	go func() {
		time.Sleep(2 * time.Second)
		ch2 <- "来自 channel 2"
	}()

	// 使用 select 等待多个 channel
	for i := 0; i < 2; i++ {
		select {
		case msg1 := <-ch1:
			fmt.Printf("接收到: %s\n", msg1)
		case msg2 := <-ch2:
			fmt.Printf("接收到: %s\n", msg2)
		case <-time.After(3 * time.Second):
			fmt.Println("超时!")
			return
		}
	}
}

// TestSelectWithDefault 演示带 default 的 select
func TestSelectWithDefault(t *testing.T) {
	fmt.Println("\n=== 带 Default 的 Select 示例 ===")

	ch := make(chan string)

	// 非阻塞发送
	select {
	case ch <- "Hello":
		fmt.Println("发送成功")
	default:
		fmt.Println("发送失败，channel 已满或无接收者")
	}

	// 非阻塞接收
	select {
	case msg := <-ch:
		fmt.Printf("接收到: %s\n", msg)
	default:
		fmt.Println("接收失败，channel 为空")
	}
}

// ============================================================================
// 4. 同步原语示例
// ============================================================================

// TestMutex 演示 Mutex 的使用
func TestMutex(t *testing.T) {
	fmt.Println("\n=== Mutex 示例 ===")

	type Counter struct {
		mu    sync.Mutex
		value int
	}

	counter := &Counter{}
	var wg sync.WaitGroup

	// 启动多个 goroutine 并发增加计数
	for i := 0; i < 1000; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			counter.mu.Lock()
			counter.value++
			counter.mu.Unlock()
		}()
	}

	wg.Wait()
	fmt.Printf("最终计数值: %d\n", counter.value)
}

// TestRWMutex 演示读写锁的使用
func TestRWMutex(t *testing.T) {
	fmt.Println("\n=== RWMutex 示例 ===")

	type SafeMap struct {
		mu   sync.RWMutex
		data map[string]int
	}

	safeMap := &SafeMap{
		data: make(map[string]int),
	}

	var wg sync.WaitGroup

	// 写操作
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			key := fmt.Sprintf("key%d", id)
			safeMap.mu.Lock()
			safeMap.data[key] = id
			fmt.Printf("写入: %s = %d\n", key, id)
			safeMap.mu.Unlock()
		}(i)
	}

	// 读操作
	for i := 0; i < 20; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			key := fmt.Sprintf("key%d", id%10)
			safeMap.mu.RLock()
			value, exists := safeMap.data[key]
			if exists {
				fmt.Printf("读取: %s = %d\n", key, value)
			}
			safeMap.mu.RUnlock()
		}(i)
	}

	wg.Wait()
}

// TestOnce 演示 sync.Once 的使用
func TestOnce(t *testing.T) {
	fmt.Println("\n=== sync.Once 示例 ===")

	var once sync.Once
	var instance *string

	getInstance := func() *string {
		once.Do(func() {
			fmt.Println("创建单例实例")
			value := "singleton instance"
			instance = &value
		})
		return instance
	}

	var wg sync.WaitGroup

	// 多个 goroutine 同时获取单例
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			inst := getInstance()
			fmt.Printf("Goroutine %d 获取到实例: %p\n", id, inst)
		}(i)
	}

	wg.Wait()
}

// TestWaitGroup 演示 WaitGroup 的使用
func TestWaitGroup(t *testing.T) {
	fmt.Println("\n=== WaitGroup 示例 ===")

	var wg sync.WaitGroup

	// 启动多个工作者
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			fmt.Printf("工作者 %d 开始工作\n", id)
			time.Sleep(time.Duration(rand.Intn(1000)) * time.Millisecond)
			fmt.Printf("工作者 %d 完成工作\n", id)
		}(i)
	}

	fmt.Println("等待所有工作者完成...")
	wg.Wait()
	fmt.Println("所有工作者已完成")
}

// ============================================================================
// 5. 原子操作示例
// ============================================================================

// TestAtomicOperations 演示原子操作
func TestAtomicOperations(t *testing.T) {
	fmt.Println("\n=== 原子操作示例 ===")

	var counter int64
	var wg sync.WaitGroup

	// 使用原子操作进行并发计数
	for i := 0; i < 1000; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			atomic.AddInt64(&counter, 1)
		}()
	}

	wg.Wait()

	finalValue := atomic.LoadInt64(&counter)
	fmt.Printf("原子操作最终计数: %d\n", finalValue)

	// 比较并交换操作
	var value int64 = 10
	swapped := atomic.CompareAndSwapInt64(&value, 10, 20)
	fmt.Printf("CAS 操作成功: %v, 新值: %d\n", swapped, atomic.LoadInt64(&value))
}

// ============================================================================
// 6. 并发模式示例
// ============================================================================

// TestProducerConsumer 演示生产者-消费者模式
func TestProducerConsumer(t *testing.T) {
	fmt.Println("\n=== 生产者-消费者模式示例 ===")

	ch := make(chan int, 10)
	var wg sync.WaitGroup

	// 生产者
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer close(ch)

		for i := 0; i < 20; i++ {
			ch <- i
			fmt.Printf("生产: %d\n", i)
			time.Sleep(50 * time.Millisecond)
		}
		fmt.Println("生产者完成")
	}()

	// 消费者
	for i := 0; i < 3; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			for value := range ch {
				fmt.Printf("消费者 %d 消费: %d\n", id, value)
				time.Sleep(100 * time.Millisecond)
			}
			fmt.Printf("消费者 %d 完成\n", id)
		}(i)
	}

	wg.Wait()
}

// TestWorkerPool 演示工作池模式
func TestWorkerPool(t *testing.T) {
	fmt.Println("\n=== 工作池模式示例 ===")

	const numWorkers = 3
	const numJobs = 10

	jobs := make(chan int, numJobs)
	results := make(chan int, numJobs)

	// 启动工作者
	var wg sync.WaitGroup
	for w := 1; w <= numWorkers; w++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for job := range jobs {
				fmt.Printf("工作者 %d 处理任务 %d\n", id, job)
				time.Sleep(100 * time.Millisecond)
				results <- job * job // 计算平方
			}
		}(w)
	}

	// 发送任务
	go func() {
		for j := 1; j <= numJobs; j++ {
			jobs <- j
		}
		close(jobs)
	}()

	// 等待工作者完成
	go func() {
		wg.Wait()
		close(results)
	}()

	// 收集结果
	for result := range results {
		fmt.Printf("结果: %d\n", result)
	}
}

// TestFanInFanOut 演示扇入扇出模式
func TestFanInFanOut(t *testing.T) {
	fmt.Println("\n=== 扇入扇出模式示例 ===")

	// 输入数据
	input := make(chan int)

	// 扇出：一个输入分发到多个处理器
	processor1 := make(chan int)
	processor2 := make(chan int)

	// 启动处理器
	go func() {
		for value := range processor1 {
			fmt.Printf("处理器1 处理: %d -> %d\n", value, value*2)
			time.Sleep(100 * time.Millisecond)
		}
	}()

	go func() {
		for value := range processor2 {
			fmt.Printf("处理器2 处理: %d -> %d\n", value, value*3)
			time.Sleep(150 * time.Millisecond)
		}
	}()

	// 分发数据
	go func() {
		defer close(processor1)
		defer close(processor2)

		for i := 1; i <= 5; i++ {
			processor1 <- i
			processor2 <- i
		}
	}()

	time.Sleep(1 * time.Second)
}

// ============================================================================
// 7. 并发控制示例
// ============================================================================

// TestConcurrencyControl 演示并发数量控制
func TestConcurrencyControl(t *testing.T) {
	fmt.Println("\n=== 并发控制示例 ===")

	const maxConcurrency = 3
	const totalTasks = 10

	// 使用有缓冲 channel 作为信号量
	semaphore := make(chan struct{}, maxConcurrency)
	var wg sync.WaitGroup

	for i := 0; i < totalTasks; i++ {
		wg.Add(1)
		go func(taskID int) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }() // 释放信号量

			fmt.Printf("任务 %d 开始执行\n", taskID)
			time.Sleep(500 * time.Millisecond)
			fmt.Printf("任务 %d 执行完成\n", taskID)
		}(i)
	}

	wg.Wait()
	fmt.Println("所有任务完成")
}

// TestContextControl 演示 Context 控制
func TestContextControl(t *testing.T) {
	fmt.Println("\n=== Context 控制示例 ===")

	// 创建带超时的 context
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	var wg sync.WaitGroup

	// 启动多个工作者
	for i := 0; i < 3; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			for {
				select {
				case <-ctx.Done():
					fmt.Printf("工作者 %d 收到取消信号: %v\n", id, ctx.Err())
					return
				default:
					fmt.Printf("工作者 %d 正在工作...\n", id)
					time.Sleep(500 * time.Millisecond)
				}
			}
		}(i)
	}

	wg.Wait()
	fmt.Println("所有工作者已停止")
}

// ============================================================================
// 8. 常见陷阱和错误示例
// ============================================================================

// TestRaceCondition 演示竞态条件
func TestRaceCondition(t *testing.T) {
	fmt.Println("\n=== 竞态条件示例 ===")

	// ❌ 错误示例：存在竞态条件
	fmt.Println("错误示例（存在竞态条件）:")
	var counter1 int
	var wg1 sync.WaitGroup

	for i := 0; i < 1000; i++ {
		wg1.Add(1)
		go func() {
			defer wg1.Done()
			counter1++ // 竞态条件
		}()
	}
	wg1.Wait()
	fmt.Printf("不安全计数结果: %d\n", counter1)

	// ✅ 正确示例：使用原子操作
	fmt.Println("\n正确示例（使用原子操作）:")
	var counter2 int64
	var wg2 sync.WaitGroup

	for i := 0; i < 1000; i++ {
		wg2.Add(1)
		go func() {
			defer wg2.Done()
			atomic.AddInt64(&counter2, 1)
		}()
	}
	wg2.Wait()
	fmt.Printf("安全计数结果: %d\n", atomic.LoadInt64(&counter2))
}

// TestDeadlock 演示死锁（注意：这个测试会被注释掉，因为会导致死锁）
func TestDeadlock(t *testing.T) {
	fmt.Println("\n=== 死锁示例（已注释避免真正死锁） ===")

	// ❌ 这会导致死锁，所以注释掉
	/*
	ch := make(chan int)
	ch <- 1  // 死锁：无缓冲 channel，没有接收者
	fmt.Println(<-ch)
	*/

	// ✅ 正确示例：使用有缓冲 channel 或 goroutine
	fmt.Println("正确示例1（有缓冲 channel）:")
	ch1 := make(chan int, 1)
	ch1 <- 1
	fmt.Printf("接收到: %d\n", <-ch1)

	fmt.Println("\n正确示例2（使用 goroutine）:")
	ch2 := make(chan int)
	go func() {
		ch2 <- 2
	}()
	fmt.Printf("接收到: %d\n", <-ch2)
}

// TestGoroutineLeak 演示 goroutine 泄漏
func TestGoroutineLeak(t *testing.T) {
	fmt.Println("\n=== Goroutine 泄漏示例 ===")

	initialGoroutines := runtime.NumGoroutine()
	fmt.Printf("初始 goroutine 数量: %d\n", initialGoroutines)

	// ❌ 可能导致泄漏的示例（已修复）
	fmt.Println("演示可能的泄漏场景（已修复）:")

	ch := make(chan int)
	done := make(chan bool)

	// 启动一个可能泄漏的 goroutine
	go func() {
		for {
			select {
			case value := <-ch:
				fmt.Printf("处理值: %d\n", value)
			case <-done:
				fmt.Println("Goroutine 正常退出")
				return // 正常退出，避免泄漏
			}
		}
	}()

	// 发送一些数据
	ch <- 1
	ch <- 2

	// 发送退出信号
	close(done)

	time.Sleep(100 * time.Millisecond)
	finalGoroutines := runtime.NumGoroutine()
	fmt.Printf("最终 goroutine 数量: %d\n", finalGoroutines)
}

// ============================================================================
// 9. 实际应用场景示例
// ============================================================================

// TestWebServerSimulation 模拟 Web 服务器并发处理
func TestWebServerSimulation(t *testing.T) {
	fmt.Println("\n=== Web 服务器并发处理模拟 ===")

	// 模拟请求处理函数
	handleRequest := func(requestID int) {
		fmt.Printf("开始处理请求 %d\n", requestID)
		// 模拟处理时间
		time.Sleep(time.Duration(rand.Intn(500)) * time.Millisecond)
		fmt.Printf("完成处理请求 %d\n", requestID)
	}

	// 限制并发数量
	const maxConcurrentRequests = 5
	semaphore := make(chan struct{}, maxConcurrentRequests)
	var wg sync.WaitGroup

	// 模拟 100 个并发请求
	for i := 1; i <= 20; i++ {
		wg.Add(1)
		go func(reqID int) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			handleRequest(reqID)
		}(i)
	}

	wg.Wait()
	fmt.Println("所有请求处理完成")
}

// TestCrawlerSimulation 模拟爬虫并发抓取
func TestCrawlerSimulation(t *testing.T) {
	fmt.Println("\n=== 爬虫并发抓取模拟 ===")

	urls := []string{
		"http://example1.com",
		"http://example2.com",
		"http://example3.com",
		"http://example4.com",
		"http://example5.com",
	}

	// 模拟抓取函数
	fetchURL := func(url string) string {
		fmt.Printf("开始抓取: %s\n", url)
		// 模拟网络延迟
		time.Sleep(time.Duration(rand.Intn(1000)) * time.Millisecond)
		result := fmt.Sprintf("内容来自 %s", url)
		fmt.Printf("完成抓取: %s\n", url)
		return result
	}

	results := make(chan string, len(urls))
	var wg sync.WaitGroup

	// 并发抓取所有 URL
	for _, url := range urls {
		wg.Add(1)
		go func(u string) {
			defer wg.Done()
			result := fetchURL(u)
			results <- result
		}(url)
	}

	// 等待所有抓取完成
	go func() {
		wg.Wait()
		close(results)
	}()

	// 收集结果
	fmt.Println("\n抓取结果:")
	for result := range results {
		fmt.Printf("- %s\n", result)
	}
}

// TestChatRoomSimulation 模拟聊天室
func TestChatRoomSimulation(t *testing.T) {
	fmt.Println("\n=== 聊天室模拟 ===")

	type Message struct {
		User    string
		Content string
		Time    time.Time
	}

	// 消息广播 channel
	broadcast := make(chan Message, 10)

	// 用户连接 channel
	users := make(map[string]chan Message)
	usersMutex := sync.RWMutex{}

	// 添加用户
	addUser := func(username string) {
		usersMutex.Lock()
		users[username] = make(chan Message, 5)
		usersMutex.Unlock()
		fmt.Printf("用户 %s 加入聊天室\n", username)
	}

	// 移除用户
	removeUser := func(username string) {
		usersMutex.Lock()
		if ch, exists := users[username]; exists {
			close(ch)
			delete(users, username)
		}
		usersMutex.Unlock()
		fmt.Printf("用户 %s 离开聊天室\n", username)
	}

	// 广播消息
	go func() {
		for msg := range broadcast {
			fmt.Printf("[广播] %s: %s\n", msg.User, msg.Content)

			usersMutex.RLock()
			for username, userCh := range users {
				if username != msg.User { // 不发送给自己
					select {
					case userCh <- msg:
					default:
						// 用户 channel 满了，跳过
					}
				}
			}
			usersMutex.RUnlock()
		}
	}()

	// 模拟用户
	var wg sync.WaitGroup

	// 用户1
	wg.Add(1)
	go func() {
		defer wg.Done()
		username := "Alice"
		addUser(username)
		defer removeUser(username)

		// 发送消息
		broadcast <- Message{User: username, Content: "大家好!", Time: time.Now()}
		time.Sleep(500 * time.Millisecond)
		broadcast <- Message{User: username, Content: "今天天气不错", Time: time.Now()}
	}()

	// 用户2
	wg.Add(1)
	go func() {
		defer wg.Done()
		username := "Bob"
		addUser(username)
		defer removeUser(username)

		time.Sleep(200 * time.Millisecond)
		broadcast <- Message{User: username, Content: "你好 Alice!", Time: time.Now()}
		time.Sleep(300 * time.Millisecond)
		broadcast <- Message{User: username, Content: "是的，很棒的天气", Time: time.Now()}
	}()

	wg.Wait()
	close(broadcast)
	time.Sleep(100 * time.Millisecond)
}

// ============================================================================
// 10. 性能测试和基准测试
// ============================================================================

// BenchmarkChannelVsMutex 比较 Channel 和 Mutex 的性能
func BenchmarkChannelVsMutex(b *testing.B) {
	// Channel 方式
	b.Run("Channel", func(b *testing.B) {
		ch := make(chan int, 1)
		ch <- 0

		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				value := <-ch
				value++
				ch <- value
			}
		})
	})

	// Mutex 方式
	b.Run("Mutex", func(b *testing.B) {
		var mu sync.Mutex
		var value int

		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				mu.Lock()
				value++
				mu.Unlock()
			}
		})
	})

	// 原子操作方式
	b.Run("Atomic", func(b *testing.B) {
		var value int64

		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				atomic.AddInt64(&value, 1)
			}
		})
	})
}

// BenchmarkGoroutineCreation 测试 Goroutine 创建性能
func BenchmarkGoroutineCreation(b *testing.B) {
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			var wg sync.WaitGroup
			wg.Add(1)
			go func() {
				defer wg.Done()
				// 空操作
			}()
			wg.Wait()
		}
	})
}

// TestGoroutineMemoryUsage 测试 Goroutine 内存使用
func TestGoroutineMemoryUsage(t *testing.T) {
	fmt.Println("\n=== Goroutine 内存使用测试 ===")

	var m1, m2 runtime.MemStats

	// 获取初始内存状态
	runtime.GC()
	runtime.ReadMemStats(&m1)

	const numGoroutines = 10000
	var wg sync.WaitGroup

	// 创建大量 goroutine
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			time.Sleep(100 * time.Millisecond)
		}()
	}

	// 获取创建后的内存状态
	runtime.ReadMemStats(&m2)

	fmt.Printf("创建 %d 个 goroutine:\n", numGoroutines)
	fmt.Printf("内存增长: %.2f KB\n", float64(m2.Alloc-m1.Alloc)/1024)
	fmt.Printf("平均每个 goroutine: %.2f bytes\n", float64(m2.Alloc-m1.Alloc)/numGoroutines)

	wg.Wait()
}

// ============================================================================
// 11. 高级并发模式
// ============================================================================

// TestPipeline 演示管道模式
func TestPipeline(t *testing.T) {
	fmt.Println("\n=== 管道模式示例 ===")

	// 阶段1：生成数字
	generate := func() <-chan int {
		out := make(chan int)
		go func() {
			defer close(out)
			for i := 1; i <= 10; i++ {
				out <- i
			}
		}()
		return out
	}

	// 阶段2：计算平方
	square := func(in <-chan int) <-chan int {
		out := make(chan int)
		go func() {
			defer close(out)
			for n := range in {
				out <- n * n
			}
		}()
		return out
	}

	// 阶段3：过滤偶数
	filterEven := func(in <-chan int) <-chan int {
		out := make(chan int)
		go func() {
			defer close(out)
			for n := range in {
				if n%2 == 0 {
					out <- n
				}
			}
		}()
		return out
	}

	// 构建管道
	numbers := generate()
	squares := square(numbers)
	evens := filterEven(squares)

	// 消费结果
	fmt.Println("管道处理结果:")
	for result := range evens {
		fmt.Printf("结果: %d\n", result)
	}
}

// TestRateLimiter 演示限流器
func TestRateLimiter(t *testing.T) {
	fmt.Println("\n=== 限流器示例 ===")

	// 创建限流器：每秒最多 3 个请求
	limiter := time.NewTicker(time.Second / 3)
	defer limiter.Stop()

	requests := make(chan int, 10)

	// 发送请求
	go func() {
		defer close(requests)
		for i := 1; i <= 10; i++ {
			requests <- i
		}
	}()

	// 处理请求（受限流控制）
	for req := range requests {
		<-limiter.C // 等待限流器允许
		fmt.Printf("处理请求 %d (时间: %s)\n", req, time.Now().Format("15:04:05.000"))
	}
}

// TestCircuitBreaker 演示断路器模式（简化版）
func TestCircuitBreaker(t *testing.T) {
	fmt.Println("\n=== 断路器模式示例 ===")

	type CircuitBreaker struct {
		failures    int
		maxFailures int
		timeout     time.Duration
		lastFailure time.Time
		state       string // "closed", "open", "half-open"
		mu          sync.Mutex
	}

	cb := &CircuitBreaker{
		maxFailures: 3,
		timeout:     2 * time.Second,
		state:       "closed",
	}

	// 模拟服务调用
	callService := func() error {
		// 模拟随机失败
		if rand.Float32() < 0.7 {
			return fmt.Errorf("服务调用失败")
		}
		return nil
	}

	// 通过断路器调用服务
	callWithCircuitBreaker := func() error {
		cb.mu.Lock()
		defer cb.mu.Unlock()

		// 检查断路器状态
		if cb.state == "open" {
			if time.Since(cb.lastFailure) > cb.timeout {
				cb.state = "half-open"
				fmt.Println("断路器状态: half-open")
			} else {
				return fmt.Errorf("断路器开启，拒绝请求")
			}
		}

		err := callService()
		if err != nil {
			cb.failures++
			cb.lastFailure = time.Now()

			if cb.failures >= cb.maxFailures {
				cb.state = "open"
				fmt.Printf("断路器开启 (失败次数: %d)\n", cb.failures)
			}
			return err
		}

		// 成功调用，重置状态
		cb.failures = 0
		cb.state = "closed"
		return nil
	}

	// 测试断路器
	for i := 1; i <= 10; i++ {
		err := callWithCircuitBreaker()
		if err != nil {
			fmt.Printf("调用 %d 失败: %v\n", i, err)
		} else {
			fmt.Printf("调用 %d 成功\n", i)
		}
		time.Sleep(300 * time.Millisecond)
	}
}
